package com.trs.ai.moye.out.feign;

import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.data.indicator.entity.IndicatorDataSearchParams;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * BI引擎feign客户端
 *
 * <AUTHOR>
 * @since 2025/07/09
 */
@FeignClient(name = "moye-bi-engine", path = "/bi-engine", contextId = "bi")
public interface BiEngineFeign {

    /**
     * 指标查询 v1
     *
     * @param connectionId 连接id
     * @param request      请求
     * @return {@link PageResponse }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2025/07/15 17:20:48
     */
    @PostMapping("/indicator/query/{connectionId}/v1")
    PageResponse<Map<String, Object>> indicatorQueryV1(@PathVariable("connectionId") Integer connectionId,
        @RequestBody IndicatorDataSearchParams request);

    /**
     * 指标查询
     *
     * @param connectionId 连接id
     * @param request      请求
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @PostMapping("/indicator/query/{connectionId}")
    PageResponse<Map<String, Object>> indicatorQuery(@PathVariable("connectionId") Integer connectionId,
        @RequestBody IndicatorDataSearchParams request);

    /**
     * 指标查询（带分组）
     *
     * @param connectionId 连接id
     * @param request      请求
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @PostMapping("/indicator/query-with-grouping/{connectionId}")
    PageResponse<Map<String, Object>> indicatorQueryWithGrouping(@PathVariable("connectionId") Integer connectionId,
        @RequestBody IndicatorDataSearchParams request);
}
