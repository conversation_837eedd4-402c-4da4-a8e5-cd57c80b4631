package com.trs.ai.moye.out.request;

import com.trs.ai.moye.out.constants.OutIndicatorType;
import com.trs.ai.moye.out.entity.IndicatorQueryCondition;
import com.trs.ai.moye.out.entity.OutIndicatorTimeRangeParams;
import com.trs.moye.base.data.indicator.entity.IndicatorSortField;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.service.entity.Condition;
import com.trs.moye.base.data.service.entity.DataServiceField;
import com.trs.moye.base.data.service.entity.ValueObject;
import com.trs.moye.base.data.service.enums.DataServiceConditionType;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

/**
 * OUT 指标查询请求
 *
 * <AUTHOR>
 * @since 2025/05/30 15:24:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OutIndicatorQueryRequest {

    /**
     * 类型，查询数据服务或指标
     */
    private OutIndicatorType type;

    /**
     * 时间范围
     */
    @Valid
    private OutIndicatorTimeRangeRequest timeRange;

    /**
     * 目标周期列表，如果该值不为空，则只查询这一个周期的数据
     */
    private List<OutIndicatorTimeRangeParams> targetPeriods;

    /**
     * 返回字段
     */
    private List<String> returnFields;

    /**
     * 排序参数
     */
    private IndicatorSortField sortParams;

    /**
     * 搜索参数
     */
    private List<IndicatorQueryCondition> searchParams;

    /**
     * 结果集大小
     */
    private Integer size;

    /**
     * 是否需要分组
     * <p>
     * 控制指标数据查询时是否执行分组逻辑：
     * <ul>
     *   <li>true：执行分组逻辑，根据统计维度对数据进行分组</li>
     *   <li>false或null：跳过分组逻辑，直接返回原始数据（默认行为）</li>
     * </ul>
     * </p>
     */
    private Boolean needGroup;

    /**
     * 到 条件
     *
     * @param indicatorFields 指示器字段
     * @return {@link List }<{@link Condition }>
     * <AUTHOR>
     * @since 2025/06/03 14:20:31
     */
    public List<Condition> toConditions(List<? extends DataModelField> indicatorFields) {
        Map<String, DataModelField> fieldMap = indicatorFields.stream()
            .collect(Collectors.toMap(DataModelField::getEnName, field -> field));
        List<Condition> conditions = new ArrayList<>();
        for (IndicatorQueryCondition condition : searchParams) {

            DataModelField field = fieldMap.get(condition.getField());
            DataServiceField dataServiceField = new DataServiceField();
            dataServiceField.setEnName(condition.getField());
            dataServiceField.setZhName(field != null ? field.getZhName() : condition.getField());
            dataServiceField.setType(field != null ? field.getType() : null);
            dataServiceField.setTypeName(field != null ? field.getTypeName() : null);
            Condition conditionToAdd = new Condition();
            conditionToAdd.setType(DataServiceConditionType.EXPRESSION);
            conditionToAdd.setKey(dataServiceField);
            conditionToAdd.setOperator(condition.getOperator());

            List<String> values = condition.getValues();
            if (CollectionUtils.isNotEmpty(values)) {
                conditionToAdd.setValues(values.stream().map(ValueObject::new).toList());
            }
            conditions.add(conditionToAdd);
            conditions.add(Condition.AND);
        }
        // 移除最后一个 AND
        if (!conditions.isEmpty() && conditions.get(conditions.size() - 1).getKey() == null) {
            conditions.remove(conditions.size() - 1);
        }
        return conditions;
    }
}
