package com.trs.ai.moye.out.service;

import com.trs.ai.moye.common.enums.SortOrder;
import com.trs.ai.moye.data.model.service.IndicatorModelService;
import com.trs.ai.moye.data.service.dao.DataServiceMapper;
import com.trs.ai.moye.data.service.dto.DataServiceDto;
import com.trs.ai.moye.data.service.entity.DataServiceField;
import com.trs.ai.moye.data.service.entity.DataServiceSortField;
import com.trs.ai.moye.data.service.entity.params.DataServiceConfigParams;
import com.trs.ai.moye.data.service.entity.params.ServiceQueryParams;
import com.trs.ai.moye.data.service.enums.ServiceConfigType;
import com.trs.ai.moye.data.service.request.ability.DataServiceInvokeRequest;
import com.trs.ai.moye.out.feign.BiEngineFeign;
import com.trs.ai.moye.out.request.OutIndicatorCurrentPeriodRequest;
import com.trs.ai.moye.out.request.OutIndicatorFieldValueRequest;
import com.trs.ai.moye.out.request.OutIndicatorQueryRequest;
import com.trs.ai.moye.out.request.OutIndicatorTimeRangeRequest;
import com.trs.ai.moye.out.response.OutIndicatorAndServiceResponse;
import com.trs.ai.moye.out.response.OutIndicatorBasicResponse;
import com.trs.ai.moye.out.response.OutIndicatorCurrentPeriod;
import com.trs.ai.moye.out.response.StatisticPeriodResponse;
import com.trs.ai.moye.out.util.indicator.TimeRangeCalculator;
import com.trs.ai.moye.storageengine.feign.SearchFeign;
import com.trs.ai.moye.storageengine.request.ConditionSearchParams;
import com.trs.ai.moye.storageengine.response.StorageSearchResponse;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.data.indicator.dao.IndicatorConfigMapper;
import com.trs.moye.base.data.indicator.dao.IndicatorPeriodConfigMapper;
import com.trs.moye.base.data.indicator.entity.DataModelIndicatorFieldResponse;
import com.trs.moye.base.data.indicator.entity.IndicatorConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorDataSearchParams;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfig;
import com.trs.moye.base.data.indicator.entity.IndicatorPeriodConfigEntity;
import com.trs.moye.base.data.indicator.entity.IndicatorSortField;
import com.trs.moye.base.data.indicator.entity.IndicatorStatisticStrategyInfo;
import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.IndicatorPeriodType;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.AbstractField;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.service.entity.Condition;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.dao.DataStorageMapper;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

/**
 * out 指标服务实现
 *
 * <AUTHOR>
 * @since 2025/06/03 11:31:08
 */
@Service
public class OutIndicatorServiceImpl implements OutIndicatorService {

    @Resource
    private IndicatorPeriodConfigMapper indicatorPeriodConfigMapper;

    @Resource
    private IndicatorConfigMapper indicatorConfigMapper;

    @Resource
    private DataStorageMapper dataStorageMapper;

    @Resource
    private IndicatorModelService indicatorModelService;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataServiceMapper dataServiceMapper;

    @Resource
    private OutApiService outApiService;

    @Resource
    private SearchFeign searchFeign;

    @Resource
    private BiEngineFeign biEngineFeign;

    @Override
    public List<Map<String, Object>> queryIndicatorData(OutIndicatorQueryRequest request, Integer dataModelId) {
        return queryIndicatorDataFromBiEngine(request, dataModelId, false).getItems();
    }

    @Override
    public List<Map<String, Object>> queryIndicatorDataWithGrouping(OutIndicatorQueryRequest request,
        Integer dataModelId) {
        return queryIndicatorDataFromBiEngine(request, dataModelId, true).getItems();
    }

    private PageResponse<Map<String, Object>> queryIndicatorDataFromBiEngine(OutIndicatorQueryRequest request,
        Integer dataModelId, boolean withGrouping) {
        // 获取指标配置
        IndicatorConfig indicatorConfig = indicatorConfigMapper.selectByDataModelId(dataModelId);
        IndicatorStatisticStrategyInfo statisticStrategyInfo = indicatorConfig.getStatisticStrategyInfo();
        IndicatorPeriodType periodType = statisticStrategyInfo.getPeriod();
        IndicatorPeriodConfigEntity periodConfig = indicatorPeriodConfigMapper.selectByPeriodType(periodType.name());

        OutIndicatorTimeRangeRequest timeRange = request.getTimeRange();
        if (Objects.isNull(timeRange)) {
            throw new BizException("时间范围(timeRange)不能为空！");
        }
        StatisticPeriod statisticPeriod = getStatisticPeriod(periodType, timeRange);

        List<DataModelIndicatorFieldResponse> indicatorFields = indicatorModelService.getIndicatorFields(dataModelId);
        List<DataStorage> dataStorages = dataStorageMapper.selectByDataModelIdWithConnection(dataModelId);
        if (dataStorages.isEmpty()) {
            throw new BizException("数据来源的存储点为空！");
        }
        Boolean needGroup = request.getNeedGroup() != null ? request.getNeedGroup() : true;
        IndicatorDataSearchParams searchParams = IndicatorDataSearchParams.builder().dataModelId(dataModelId)
            .tableName(dataStorages.get(0).getEnName())
            .conditions(request.toConditions(indicatorFields))
            .returnFields(request.getReturnFields())
            .sortField(request.getSortParams())
            .statisticPeriod(statisticPeriod)
            .size(request.getSize())
            .needGroup(needGroup)
            .build();

        if (CollectionUtils.isNotEmpty(request.getTargetPeriods())) {
            List<StatisticPeriod> targetPeriods = request.getTargetPeriods().stream()
                .map(p -> TimeRangeCalculator.calculatePeriod(p, periodConfig.getConfig()))
                .toList();
            searchParams.setTargetPeriods(targetPeriods);
        }

        Integer connectionId = dataStorages.get(0).getConnectionId();
        PageResponse<Map<String, Object>> response;
        if (withGrouping) {
            response = biEngineFeign.indicatorQueryWithGrouping(connectionId, searchParams);
        } else {
            // 暂时使用v1版本的接口
            response = biEngineFeign.indicatorQueryV1(connectionId, searchParams);
//            response = biEngineFeign.indicatorQuery(connectionId, searchParams);
        }

        return response;
    }

    /**
     * 查询数据服务数据
     *
     * @param request     请求
     * @param dataModelId 数据型id
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @since 2025/06/16 18:36:52
     */
    @Override
    public List<Map<String, Object>> queryServiceData(OutIndicatorQueryRequest request, Integer dataModelId) {

        DataServiceDto dataServiceDto = dataServiceMapper.selectDtoById(dataModelId);
        DataServiceConfigParams params = dataServiceDto.getDataServiceConfig().getParams();
        List<DataServiceField> dataServiceReturnFields = new ArrayList<>();
        if (params instanceof ServiceQueryParams queryParams) {
            dataServiceReturnFields = queryParams.getReturnFields();
        }
        List<Condition> conditions = new ArrayList<>();
        conditions.add(Condition.AND);
        conditions.addAll(request.toConditions(dataServiceReturnFields.stream().
            map(DataServiceField::toDataModelField).toList()));
        PageParams pageParams = new PageParams(1, 10000); // 默认分页参数，实际使用中可以根据需要调整
        DataServiceInvokeRequest invokeRequest = new DataServiceInvokeRequest();
        invokeRequest.setCondition(conditions);
        invokeRequest.setPageParams(pageParams);
        List<String> returnFields = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getReturnFields())) {
            returnFields = request.getReturnFields();
        }
        Map<String, DataServiceField> fieldMap = dataServiceReturnFields.stream()
            .collect(Collectors.toMap(DataServiceField::getEnName, field -> field));
        invokeRequest.setReturnFields(
            returnFields.stream().map(fieldMap::get).toList());
        Map<String, List<String>> replaceFields = new HashMap<>();
        StatisticPeriodResponse statisticPeriod = getStatisticPeriod(request.getTimeRange());
        replaceFields.put("beginTime", List.of(DateTimeUtils.formatStr(statisticPeriod.getBeginTime())));
        replaceFields.put("endTime", List.of(DateTimeUtils.formatStr(statisticPeriod.getEndTime())));
        invokeRequest.setReplaceFields(replaceFields);
        List<DataServiceSortField> sortFields = new ArrayList<>();
        IndicatorSortField sortParams = request.getSortParams();
        if (Objects.nonNull(sortParams) && Objects.nonNull(sortParams.getField()) && Objects.nonNull(
            sortParams.getOrder())) {
            DataServiceSortField sortField = new DataServiceSortField(
                fieldMap.get(sortParams.getField()),
                SortOrder.valueOf(sortParams.getOrder().name()));
            sortFields.add(sortField);
            invokeRequest.setSortFields(sortFields);
        }
        return outApiService.getDataListByDataService(dataServiceDto, invokeRequest).getItems();
    }


    @Override
    public StatisticPeriodResponse getStatisticPeriod(OutIndicatorTimeRangeRequest timeRange) {
        if (Objects.isNull(timeRange) || Objects.isNull(timeRange.getBeginTime()) || Objects.isNull(
            timeRange.getEndTime())) {
            throw new BizException("时间范围不能为空！");
        }
        StatisticPeriod statisticPeriod = getStatisticPeriod(timeRange.getPeriodType(), timeRange);
        return new StatisticPeriodResponse(statisticPeriod.getStartTime(), statisticPeriod.getEndTime());
    }

    @NotNull
    private StatisticPeriod getStatisticPeriod(IndicatorPeriodType periodType, OutIndicatorTimeRangeRequest timeRange) {
        IndicatorPeriodConfigEntity periodConfig = indicatorPeriodConfigMapper.selectByPeriodType(periodType.name());
        return TimeRangeCalculator.calculateRange(timeRange, periodConfig.getConfig());
    }

    @Override
    public OutIndicatorCurrentPeriod getCurrentPeriodIndicator(OutIndicatorCurrentPeriodRequest request) {
        IndicatorPeriodConfig<?> config = indicatorPeriodConfigMapper.selectByPeriodType(request.getPeriod().name())
            .getConfig();
        IndicatorPeriodConfig<?> yearlyConfig = null;
        if (request.getPeriod() == IndicatorPeriodType.WEEKLY) {
            yearlyConfig = indicatorPeriodConfigMapper.selectByPeriodType(IndicatorPeriodType.YEARLY.name())
                .getConfig();
        }
        return TimeRangeCalculator.calculateCurrentPeriod(request, config, yearlyConfig);
    }

    @Override
    public List<OutIndicatorBasicResponse> getIndicatorAndService(Integer categoryId) {
        List<OutIndicatorAndServiceResponse> indicator = dataModelMapper.selectByLayerAndCategoryId(
            ModelLayer.INDICATOR, categoryId).stream().map(OutIndicatorAndServiceResponse::new).toList();

        List<OutIndicatorAndServiceResponse> dataService = dataServiceMapper.selectDtoByBusinessCategoryId(categoryId)
            .stream().map(OutIndicatorAndServiceResponse::new).toList();

        // 合并两个列表
        List<OutIndicatorBasicResponse> result = new ArrayList<>();
        result.addAll(indicator);
        result.addAll(dataService);
        return result;
    }

    @Override
    public List<String> indicatorGroupByField(OutIndicatorFieldValueRequest request, Integer dataModelId) {
        if (Objects.isNull(request) || Objects.isNull(request.getField())) {
            throw new BizException("字段名不能为空！");
        }
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        DataStorage dataStorage = dataStorageMapper.selectByDataModelIdWithConnection(dataModelId).stream().findFirst()
            .orElseThrow(() -> new BizException("数据来源的存储点为空！"));

        List<DataModelField> list = dataModel.getFields().stream()
            .filter(e -> dataStorage.getFieldIds().contains(e.getId())).toList();
        if (CollectionUtils.isNotEmpty(list) && !list.stream().map(AbstractField::getEnName).toList()
            .contains(request.getField())) {
            throw new BizException("字段 %s 在数据建模中不存在！", request.getField());
        }
        ConditionSearchParams conditionSearchParams = new ConditionSearchParams();
        conditionSearchParams.setGroupFields(List.of(request.getField()));
        conditionSearchParams.setOperationType(ServiceConfigType.COUNT);
        StorageSearchResponse storageSearchResponse = searchFeign.conditionQuery(dataStorage.getConnectionId(),
            dataStorage.getEnName(), conditionSearchParams);
        if (CollectionUtils.isNotEmpty(storageSearchResponse.getItems())) {
            return storageSearchResponse.getItems().stream().map(item -> {
                if (item.containsKey(request.getField())) {
                    return String.valueOf(item.get(request.getField()));
                } else {
                    throw new BizException("字段 %s 在数据中不存在！", request.getField());
                }
            }).toList();
        }
        return Collections.emptyList();
    }
}
