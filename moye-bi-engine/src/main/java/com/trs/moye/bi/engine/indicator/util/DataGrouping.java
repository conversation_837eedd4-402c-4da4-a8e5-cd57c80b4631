package com.trs.moye.bi.engine.indicator.util;

import static com.trs.moye.bi.engine.indicator.constant.BiIndicatorConstants.GROUP_FIELD;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * 数据分组
 *
 * <AUTHOR>
 * @since 2025/07/09
 */
@Slf4j
@Service
public class DataGrouping {

    /**
     * 指标数据分组
     * <p>
     * 根据needGroup参数控制是否执行分组逻辑：
     * <ul>
     *   <li>needGroup为true时：执行分组逻辑，根据统计维度对数据进行分组，无论结果如何都返回分组后的数据结构</li>
     *   <li>needGroup为false或null时：跳过分组逻辑，直接返回原始数据（默认行为）</li>
     * </ul>
     * </p>
     *
     * @param maps                  原始数据列表
     * @param originalDimFieldNames 统计维度字段名称列表
     * @param needGroup             是否需要分组，为null或false时跳过分组，为true时执行分组
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>> 处理后的数据列表
     */
    public List<Map<String, Object>> groupIndicatorData(List<Map<String, Object>> maps,
        List<String> originalDimFieldNames, Boolean needGroup) {

        // 检查是否需要执行分组逻辑
        if (!Boolean.TRUE.equals(needGroup)) {
            log.debug("needGroup参数为{}，跳过分组逻辑，直接返回原始数据", needGroup);
            return maps;
        }

        if (CollectionUtils.isEmpty(originalDimFieldNames) || CollectionUtils.isEmpty(maps)) {
            log.debug("维度字段为空或数据为空，直接返回原始数据");
            return maps;
        }

        log.debug("开始执行分组逻辑，维度字段数量: {}, 数据量: {}", originalDimFieldNames.size(), maps.size());

        // 使用所有维度字段进行分组
        Map<Map<String, Object>, List<Map<String, Object>>> groupedData = maps.stream()
            .collect(Collectors.groupingBy(item -> {
                Map<String, Object> key = new HashMap<>();
                for (String field : originalDimFieldNames) {
                    key.put(field, item.get(field));
                }
                return key;
            }, LinkedHashMap::new, Collectors.toList()));

        // 执行分组处理，无论结果如何都返回分组后的数据结构
        List<Map<String, Object>> groupedResult = processGroupedData(groupedData, originalDimFieldNames);

        log.debug("分组处理完成，产生了 {} 个分组", groupedResult.size());
        return groupedResult;
    }

    private List<Map<String, Object>> processGroupedData(
        Map<Map<String, Object>, List<Map<String, Object>>> groupedData, List<String> dimFieldNames) {

        return groupedData.entrySet().stream().map(entry -> {
            Map<String, Object> resultItem = new HashMap<>();
            // 优先从key中取值，如果key中没有则从value中获取
            dimFieldNames.forEach(dimField -> {
                Object value = entry.getKey().get(dimField);
                if (value == null && !entry.getValue().isEmpty()) {
                    value = entry.getValue().get(0).get(dimField);
                }
                resultItem.put(dimField, value);
            });
            List<Map<String, Object>> data = entry.getValue().stream()
                .map(HashMap::new)
                .collect(Collectors.toCollection(ArrayList::new));
            resultItem.put(GROUP_FIELD, data);
            return resultItem;
        }).collect(Collectors.toCollection(ArrayList::new));
    }
}
