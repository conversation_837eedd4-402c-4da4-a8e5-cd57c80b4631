package com.trs.moye.bi.engine.indicator.function.calculation;

import com.trs.moye.base.data.indicator.entity.StatisticPeriod;
import com.trs.moye.base.data.indicator.enums.IndicatorPeriodType;
import com.trs.moye.bi.engine.indicator.annotation.IndicatorCategory;
import com.trs.moye.bi.engine.indicator.annotation.IndicatorFunction;
import com.trs.moye.bi.engine.indicator.annotation.IndicatorParameter;
import com.trs.moye.bi.engine.indicator.context.IndicatorDataContext;
import com.trs.moye.bi.engine.indicator.entity.TimedRow;
import com.trs.moye.bi.engine.indicator.util.CalculationUtils;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.BinaryOperator;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

/**
 * 对比分析
 *
 * <AUTHOR>
 * @since 2025/07/02
 */
@IndicatorCategory(enAbbr = "comparative", name = "比较分析", description = "提供数据对比分析功能，包括环比、同比等")
public final class ComparativeAnalysis {

    private ComparativeAnalysis() {
    }

    /**
     * 获取指定字段的原始数据
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>调用通用的对比分析方法 {@link #getComparativeData}</li>
     *   <li>时间转换逻辑为返回当前时间本身</li>
     *   <li>结果计算逻辑为直接返回当前周期的值</li>
     * </ol>
     *
     * @param targetField 目标字段的名称
     * @return 包含原始数据的Map，键为行的唯一标识，值为字段值
     */
    @IndicatorFunction(
        enName = "getFieldData",
        zhName = "获取字段数据",
        description = "获取指定字段的原始数据值"
    )
    public static Map<String, Object> getFieldData(
        @IndicatorParameter(
            name = "targetField",
            description = "目标字段的名称"
        ) String targetField) {
        return getComparativeData(targetField,
            currentTime -> currentTime,
            (current, previous) -> current);
    }

    /**
     * 获取相对于当前周期N个周期之前的数据
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>从上下文获取周期类型 (日/周/月等)</li>
     *   <li>调用 {@link #getComparativeData}</li>
     *   <li>时间转换逻辑为：根据当前时间和周期类型，计算出N个周期前的时间点</li>
     *   <li>结果计算逻辑为：返回找到的历史周期的值</li>
     * </ol>
     *
     * @param targetField     目标字段的名称
     * @param previousPeriods 要回溯的周期数
     * @return 包含历史数据的Map，键为行的唯一标识，值为历史字段值
     */
    @IndicatorFunction(
        enName = "getPreviousPeriodData",
        zhName = "获取历史周期数据",
        description = "获取相对于当前周期N个周期之前的数据"
    )
    public static Map<String, Object> getPreviousPeriodData(
        @IndicatorParameter(
            name = "targetField",
            description = "目标字段的名称"
        ) String targetField,
        @IndicatorParameter(
            name = "previousPeriods",
            description = "要回溯的周期数"
        ) Integer previousPeriods) {
        Optional<IndicatorPeriodType> periodTypeOpt = IndicatorDataContext.getPeriodType();

        if (periodTypeOpt.isEmpty() || previousPeriods == null) {
            return Collections.emptyMap();
        }
        IndicatorPeriodType periodType = periodTypeOpt.get();

        return getComparativeData(targetField,
            currentTime -> StatisticPeriod.minusPeriod(periodType, previousPeriods.longValue(), currentTime),
            (current, previous) -> previous);
    }

    /**
     * 计算字段的环比增长率，使用默认精度（1位小数）
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>从上下文获取周期类型</li>
     *   <li>调用 {@link #getComparativeData}</li>
     *   <li>时间转换逻辑为：计算出上一个周期的时间点</li>
     *   <li>结果计算逻辑为：使用 (当前值 - 上期值) / 上期值 * 100% 公式计算比率</li>
     * </ol>
     *
     * @param targetField 目标字段的名称
     * @return 包含环比增长率的Map，值为格式化后的百分比字符串
     */
    @IndicatorFunction(
        enName = "getPeriodOverPeriodData",
        zhName = "获取环比数据",
        description = "计算字段的环比增长率，返回格式化的百分比字符串"
    )
    public static Map<String, Object> getPeriodOverPeriodData(
        @IndicatorParameter(
            name = "targetField",
            description = "目标字段的名称"
        ) String targetField) {
        Optional<IndicatorPeriodType> periodTypeOpt = IndicatorDataContext.getPeriodType();
        if (periodTypeOpt.isEmpty()) {
            return Collections.emptyMap();
        }
        IndicatorPeriodType periodType = periodTypeOpt.get();
        return getComparativeData(targetField,
            currentTime -> StatisticPeriod.minusPeriod(periodType, 1L, currentTime),
            (currentValue, previousValue) -> CalculationUtils.calculateSafeRatio(
                CalculationUtils.safeGetNumber(currentValue),
                CalculationUtils.safeGetNumber(previousValue))
        );
    }

    /**
     * 计算字段的环比增长率，支持自定义精度
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>从上下文获取周期类型</li>
     *   <li>调用 {@link #getComparativeData}</li>
     *   <li>时间转换逻辑为：计算出上一个周期的时间点</li>
     *   <li>结果计算逻辑为：使用 (当前值 - 上期值) / 上期值 * 100% 公式计算比率</li>
     * </ol>
     *
     * @param targetField 目标字段的名称
     * @param precision   小数位数精度，范围为0-10
     * @return 包含环比增长率的Map，值为格式化后的百分比字符串
     */
    @IndicatorFunction(
        enName = "getPeriodOverPeriodData",
        zhName = "获取环比数据",
        description = "计算字段的环比增长率，支持自定义精度，返回格式化的百分比字符串"
    )
    public static Map<String, Object> getPeriodOverPeriodData(
        @IndicatorParameter(
            name = "targetField",
            description = "目标字段的名称"
        ) String targetField,
        @IndicatorParameter(
            name = "precision",
            description = "小数位数精度，范围为0-10"
        ) Integer precision) {
        Optional<IndicatorPeriodType> periodTypeOpt = IndicatorDataContext.getPeriodType();
        if (periodTypeOpt.isEmpty()) {
            return Collections.emptyMap();
        }
        IndicatorPeriodType periodType = periodTypeOpt.get();
        int actualPrecision = (precision != null) ? precision : 1;
        return getComparativeData(targetField,
            currentTime -> StatisticPeriod.minusPeriod(periodType, 1L, currentTime),
            (currentValue, previousValue) -> CalculationUtils.calculateSafeRatio(
                CalculationUtils.safeGetNumber(currentValue),
                CalculationUtils.safeGetNumber(previousValue),
                actualPrecision)
        );
    }

    /**
     * 计算字段的同比增长率，使用默认精度（1位小数）
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>调用 {@link #getComparativeData}</li>
     *   <li>时间转换逻辑为：计算出去年同一天的时间点</li>
     *   <li>结果计算逻辑为：使用 (当前值 - 去年同期值) / 去年同期值 * 100% 公式计算比率</li>
     * </ol>
     *
     * @param targetField 目标字段的名称
     * @return 包含同比增长率的Map，值为格式化后的百分比字符串
     */
    @IndicatorFunction(
        enName = "getYearOverYearData",
        zhName = "获取同比数据",
        description = "计算字段的同比增长率，返回格式化的百分比字符串"
    )
    public static Map<String, Object> getYearOverYearData(
        @IndicatorParameter(
            name = "targetField",
            description = "目标字段的名称"
        ) String targetField) {
        return getComparativeData(targetField, currentTime -> currentTime.minusYears(1),
            (currentValue, previousValue) -> CalculationUtils.calculateSafeRatio(
                CalculationUtils.safeGetNumber(currentValue),
                CalculationUtils.safeGetNumber(previousValue))
        );
    }

    /**
     * 计算字段的同比增长率，支持自定义精度
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>调用 {@link #getComparativeData}</li>
     *   <li>时间转换逻辑为：计算出去年同一天的时间点</li>
     *   <li>结果计算逻辑为：使用 (当前值 - 去年同期值) / 去年同期值 * 100% 公式计算比率</li>
     * </ol>
     *
     * @param targetField 目标字段的名称
     * @param precision   小数位数精度，范围为0-10
     * @return 包含同比增长率的Map，值为格式化后的百分比字符串
     */
    @IndicatorFunction(
        enName = "getYearOverYearData",
        zhName = "获取同比数据",
        description = "计算字段的同比增长率，支持自定义精度，返回格式化的百分比字符串"
    )
    public static Map<String, Object> getYearOverYearData(
        @IndicatorParameter(
            name = "targetField",
            description = "目标字段的名称"
        ) String targetField,
        @IndicatorParameter(
            name = "precision",
            description = "小数位数精度，范围为0-10"
        ) Integer precision) {
        int actualPrecision = (precision != null) ? precision : 1;
        return getComparativeData(targetField, currentTime -> currentTime.minusYears(1),
            (currentValue, previousValue) -> CalculationUtils.calculateSafeRatio(
                CalculationUtils.safeGetNumber(currentValue),
                CalculationUtils.safeGetNumber(previousValue),
                actualPrecision)
        );
    }

    /**
     * 获取去年同期的数据
     * <p>
     * <b>处理流程:</b>
     * <ol>
     *   <li>调用 {@link #getComparativeData}</li>
     *   <li>时间转换逻辑为：计算出去年同一天的时间点</li>
     *   <li>结果计算逻辑为：直接返回找到的去年同期的值</li>
     * </ol>
     *
     * @param targetField 目标字段的名称
     * @return 包含去年同期数据的Map
     */
    @IndicatorFunction(
        enName = "getYearAgoData",
        zhName = "获取去年同期数据",
        description = "获取去年同期的数据值，用于同比分析"
    )
    public static Map<String, Object> getYearAgoData(
        @IndicatorParameter(
            name = "targetField",
            description = "目标字段的名称"
        ) String targetField) {
        return getComparativeData(targetField,
            currentTime -> currentTime.minusYears(1),
            (current, previous) -> previous
        );
    }


    /**
     * 通用的比较数据获取方法，是同比、环比等计算的核心
     *
     * @param targetField            目标字段
     * @param previousTimeCalculator 用于根据当前时间计算出需要对比的历史时间的函数
     * @param resultCalculator       用于根据当前值和历史值计算最终结果的函数
     * @return 包含计算结果的Map
     */
    private static Map<String, Object> getComparativeData(String targetField,
        UnaryOperator<LocalDateTime> previousTimeCalculator,
        BinaryOperator<Object> resultCalculator) {

        return IndicatorDataContext.getCalculationContext().map(context -> {
            if (context.dimKeyGenerator() == null || context.timeField() == null) {
                return Collections.<String, Object>emptyMap();
            }
            // 按维度键和时间对数据进行分组，以便高效查找
            Map<String, Map<LocalDateTime, Map<String, Object>>> dataByDimAndTime = context.allData().stream()
                .map(row -> new TimedRow(row, CalculationUtils.parseLocalDateTime(row.get(context.timeField()))))
                .filter(tr -> tr.time() != null)
                .collect(Collectors.groupingBy(
                    tr -> context.dimKeyGenerator().apply(tr.row()),
                    Collectors.toMap(TimedRow::time, TimedRow::row, (existing, replacement) -> replacement)
                ));

            Map<String, Object> resultMap = new HashMap<>();

            // 遍历每个维度的数据
            dataByDimAndTime.forEach((dimsKey, dimDataByTime) ->
                calculateForDimension(dimDataByTime, targetField, previousTimeCalculator, resultCalculator,
                    resultMap));
            return resultMap;
        }).orElse(Collections.emptyMap());
    }

    /**
     * 为单个维度的数据执行计算
     *
     * @param dimDataByTime          该维度下按时间分组的数据
     * @param targetField            目标字段
     * @param previousTimeCalculator 历史时间计算器
     * @param resultCalculator       结果计算器
     * @param resultMap              用于存储最终结果的Map
     */
    private static void calculateForDimension(Map<LocalDateTime, Map<String, Object>> dimDataByTime,
        String targetField, UnaryOperator<LocalDateTime> previousTimeCalculator,
        BinaryOperator<Object> resultCalculator,
        Map<String, Object> resultMap) {

        IndicatorDataContext.getCalculationContext()
            .ifPresent(context -> {
                // 获取当前周期数据的时间范围
                Optional<StatisticPeriod> currentPeriodTimeRange = IndicatorDataContext.getCurrentPeriodTimeRange(
                    context);
                if (currentPeriodTimeRange.isEmpty()) {
                    return;
                }

                StatisticPeriod timeRange = currentPeriodTimeRange.get();
                dimDataByTime.forEach((currentTime, currentRow) -> {
                    // 只处理当前周期数据时间范围内的数据行
                    if (currentTime.isBefore(timeRange.getStartTime())
                        || !currentTime.isBefore(timeRange.getEndTime())) {
                        return;
                    }

                    // 在当前周期中找到一行，现在查找其对应的上一周期数据
                    LocalDateTime previousTime = previousTimeCalculator.apply(currentTime);
                    Map<String, Object> previousRow = dimDataByTime.get(previousTime);

                    String currentKey = context.keyGenerator().apply(currentRow);
                    Object currentValue = CalculationUtils.getFieldValue(currentRow, targetField,
                        context.allCalculatedValues(),
                        currentKey);

                    Object previousValue = null;
                    if (previousRow != null) {
                        String previousKey = context.keyGenerator().apply(previousRow);
                        previousValue = CalculationUtils.getFieldValue(previousRow, targetField,
                            context.allCalculatedValues(),
                            previousKey);
                    }

                    Object result = resultCalculator.apply(currentValue, previousValue);
                    if (result != null) {
                        resultMap.put(context.keyGenerator().apply(currentRow), result);
                    }
                });
            });
    }

    /**
     * 计算两个指定字段之间的比率（同比增长率），使用默认精度（1位小数）
     * 公式：(field2 - field1) / field1 * 100%
     *
     * @param field1 作为基准的字段（例如，去年的数据字段）
     * @param field2 要比较的字段（例如，今年的数据字段）
     * @return 包含比率的Map，键为组合键，值为格式化后的百分比字符串
     */
    @IndicatorFunction(
        enName = "getYoYByFields",
        zhName = "按字段获取同比数据",
        description = "通过两个字段计算同比增长率，返回格式化的百分比字符串"
    )
    public static Map<String, Object> getYoYByFields(
        @IndicatorParameter(
            name = "field1",
            description = "作为基准的字段（例如，去年的数据字段）"
        ) String field1,
        @IndicatorParameter(
            name = "field2",
            description = "要比较的字段（例如，今年的数据字段）"
        ) String field2) {
        return IndicatorDataContext.getCalculationContext().map(context -> {
            Map<String, Object> resultMap = new HashMap<>();

            for (Map<String, Object> row : context.currentPeriodData()) {
                String key = context.keyGenerator().apply(row);

                Object value1Obj = CalculationUtils.getFieldValue(row, field1, context.allCalculatedValues(), key);
                Object value2Obj = CalculationUtils.getFieldValue(row, field2, context.allCalculatedValues(), key);

                Number value1 = CalculationUtils.safeGetNumber(value1Obj);
                Number value2 = CalculationUtils.safeGetNumber(value2Obj);

                String ratio = CalculationUtils.calculateSafeRatio(value2, value1);
                resultMap.put(key, ratio);
            }
            return resultMap;
        }).orElse(Collections.emptyMap());
    }

    /**
     * 计算两个指定字段之间的比率（同比增长率），支持自定义精度
     * 公式：(field2 - field1) / field1 * 100%
     *
     * @param field1    作为基准的字段（例如，去年的数据字段）
     * @param field2    要比较的字段（例如，今年的数据字段）
     * @param precision 小数位数精度，范围为0-10
     * @return 包含比率的Map，键为组合键，值为格式化后的百分比字符串
     */
    @IndicatorFunction(
        enName = "getYoYByFields",
        zhName = "按字段获取同比数据",
        description = "通过两个字段计算同比增长率，支持自定义精度，返回格式化的百分比字符串"
    )
    public static Map<String, Object> getYoYByFields(
        @IndicatorParameter(
            name = "field1",
            description = "作为基准的字段（例如，去年的数据字段）"
        ) String field1,
        @IndicatorParameter(
            name = "field2",
            description = "要比较的字段（例如，今年的数据字段）"
        ) String field2,
        @IndicatorParameter(
            name = "precision",
            description = "小数位数精度，范围为0-10"
        ) Integer precision) {
        return IndicatorDataContext.getCalculationContext().map(context -> {
            Map<String, Object> resultMap = new HashMap<>();
            int actualPrecision = (precision != null) ? precision : 1;

            for (Map<String, Object> row : context.currentPeriodData()) {
                String key = context.keyGenerator().apply(row);

                Object value1Obj = CalculationUtils.getFieldValue(row, field1, context.allCalculatedValues(), key);
                Object value2Obj = CalculationUtils.getFieldValue(row, field2, context.allCalculatedValues(), key);

                Number value1 = CalculationUtils.safeGetNumber(value1Obj);
                Number value2 = CalculationUtils.safeGetNumber(value2Obj);

                String ratio = CalculationUtils.calculateSafeRatio(value2, value1, actualPrecision);
                resultMap.put(key, ratio);
            }
            return resultMap;
        }).orElse(Collections.emptyMap());
    }
}
